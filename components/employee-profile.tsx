"use client"

import { Em<PERSON>loyee, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  Briefcase, 
  Mail, 
  MapPin, 
  Building, 
  DollarSign,
  Linkedin,
  Twitter,
  BarChart3,
  Edit,
  Eye,
  EyeOff,
  Share2,
  Copy,
  Check
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { getCurrentUser } from "@/lib/auth"

interface EmployeeProfileProps {
  employee: Employee & {
    kpis?: EmployeeKPI[]
  }
  canEdit?: boolean
  currentUserRole?: string
}

export function EmployeeProfile({ 
  employee, 
  canEdit = false,
  currentUserRole
}: EmployeeProfileProps) {
  const [showRate, setShowRate] = useState(false)
  const [copied, setCopied] = useState(false)
  
  // Check if current user can view rates (role-based access only)
  const canViewRates = 
    currentUserRole === 'accountant' || 
    currentUserRole === 'hr-admin' || 
    currentUserRole === 'super-admin'

  const handleShareProfile = async () => {
    const shareUrl = `${window.location.origin}/profile/${employee.id}`
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header Card */}
      <Card>
        <CardHeader className="text-center sm:text-left">
          <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-4">
            <div className="space-y-2 text-center sm:text-left">
              <CardTitle className="text-3xl font-bold">
                {employee.firstName} {employee.lastName}
              </CardTitle>
              <CardDescription className="text-lg">
                {employee.departmentName || 'No Department'}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {canEdit && (
                <Link href={`/dashboard/employees/${employee.id}/profile/edit`}>
                  <Button variant="outline" size="sm" className="shrink-0">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>
                </Link>
              )}
              <Button 
                variant="outline" 
                size="sm" 
                className="shrink-0"
                onClick={handleShareProfile}
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Profile
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Contact Information */}
          <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6 text-center sm:text-left">
            {employee.email && (
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <a href={`mailto:${employee.email}`} className="text-base hover:underline text-blue-600">
                  {employee.email}
                </a>
              </div>
            )}
            <div className="flex items-center gap-3">
              <Building className="h-5 w-5 text-muted-foreground" />
              <span className="text-base font-medium">{employee.departmentName || 'No Department'}</span>
            </div>
          </div>

          {/* Social Links */}
          {(employee.linkedinUrl || employee.twitterUrl) && (
            <div className="flex justify-center sm:justify-start gap-6">
              {employee.linkedinUrl && (
                <a 
                  href={employee.linkedinUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-base text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                >
                  <Linkedin className="h-5 w-5" />
                  LinkedIn
                </a>
              )}
              {employee.twitterUrl && (
                <a 
                  href={employee.twitterUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-base text-sky-500 hover:text-sky-700 hover:underline transition-colors"
                >
                  <Twitter className="h-5 w-5" />
                  X (Twitter)
                </a>
              )}
            </div>
          )}

          <Separator />

          {/* Compensation (with access control) */}
          {canViewRates && (
            <div className="space-y-3 text-center sm:text-left">
              <div className="flex items-center justify-center sm:justify-start gap-3">
                <DollarSign className="h-5 w-5 text-muted-foreground" />
                <span className="text-base font-medium">Compensation</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRate(!showRate)}
                  className="h-auto p-2 hover:bg-muted/50"
                >
                  {showRate ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {showRate && (
                <div className="flex justify-center sm:justify-start">
                  <Badge variant="secondary" className="text-base px-4 py-2">
                    ${employee.rate.toLocaleString()} / {employee.compensation}
                  </Badge>
                </div>
              )}
            </div>
          )}

          {/* Bio */}
          {employee.bio && (
            <>
              <Separator />
              <div className="space-y-4 text-center sm:text-left">
                <h3 className="text-lg font-semibold">About</h3>
                <p className="text-base text-muted-foreground whitespace-pre-wrap leading-relaxed max-w-3xl mx-auto sm:mx-0">
                  {employee.bio}
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* KPIs Card */}
      {employee.kpis && employee.kpis.length > 0 && (
        <Card>
          <CardHeader className="text-center sm:text-left">
            <CardTitle className="flex items-center justify-center sm:justify-start gap-3 text-xl">
              <BarChart3 className="h-6 w-6" />
              Key Performance Indicators
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {employee.kpis.map((kpi) => (
                <div key={kpi.id} className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">{kpi.kpiName}</h4>
                      {kpi.description && (
                        <p className="text-xs text-muted-foreground">{kpi.description}</p>
                      )}
                    </div>
                    {kpi.period && (
                      <Badge variant="outline" className="text-xs">
                        {kpi.period}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    {kpi.kpiValue && (
                      <div>
                        <span className="text-muted-foreground">Current: </span>
                        <span className="font-medium">
                          {kpi.kpiValue}
                          {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                        </span>
                      </div>
                    )}
                    {kpi.kpiTarget && (
                      <div>
                        <span className="text-muted-foreground">Target: </span>
                        <span className="font-medium">
                          {kpi.kpiTarget}
                          {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                        </span>
                      </div>
                    )}
                  </div>
                  {(kpi.kpiValue && kpi.kpiTarget && !isNaN(Number(kpi.kpiValue)) && !isNaN(Number(kpi.kpiTarget))) && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all"
                        style={{ 
                          width: `${Math.min(100, (Number(kpi.kpiValue) / Number(kpi.kpiTarget)) * 100)}%` 
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employment Status */}
      <Card>
        <CardHeader className="text-center sm:text-left">
          <CardTitle className="text-xl">Employment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-center sm:text-left">
              <span className="text-muted-foreground font-medium">Status</span>
              <Badge variant={employee.active ? "default" : "secondary"} className="self-center sm:self-auto text-base px-3 py-1">
                {employee.active ? "Active" : "Inactive"}
              </Badge>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-center sm:text-left">
              <span className="text-muted-foreground font-medium">Employment Type</span>
              <span className="font-semibold capitalize text-lg">{employee.compensation}</span>
            </div>
            {employee.managers && employee.managers.length > 0 && (
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 text-center sm:text-left">
                <span className="text-muted-foreground font-medium">Reports To</span>
                <div className="flex flex-col items-center sm:items-end gap-2">
                  {employee.managers.map((manager) => (
                    <div key={manager.id} className="flex items-center gap-2">
                      <span className="font-semibold text-base">{manager.managerName}</span>
                      {manager.isPrimary && (
                        <Badge variant="outline" className="text-xs">Primary</Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}